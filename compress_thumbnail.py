#!/usr/bin/env python3
"""
Thumbnail Compressor for YouTube
Compresses images to meet YouTube's 2MB limit while maintaining quality.
"""

import os
from PIL import Image
import sys

def compress_image(input_path, output_path=None, max_size_mb=2):
    """
    Compress an image to meet YouTube's size requirements
    
    Args:
        input_path: Path to the input image
        output_path: Path for the compressed image (optional)
        max_size_mb: Maximum file size in MB (default: 2 for YouTube)
    """
    
    # Check if input file exists
    if not os.path.exists(input_path):
        print(f"❌ Error: File not found: {input_path}")
        return False
    
    # Get file info
    original_size = os.path.getsize(input_path) / (1024 * 1024)  # Size in MB
    print(f"📁 Original file: {os.path.basename(input_path)}")
    print(f"📊 Original size: {original_size:.2f} MB")
    
    # Check if compression is needed
    if original_size <= max_size_mb:
        print(f"✅ File is already under {max_size_mb}MB limit!")
        return True
    
    # Set output path if not provided
    if output_path is None:
        name, ext = os.path.splitext(input_path)
        output_path = f"{name}_compressed{ext}"
    
    try:
        # Open and process image
        print(f"🔄 Compressing image...")
        
        with Image.open(input_path) as img:
            # Convert to RGB if necessary (for JPEG compatibility)
            if img.mode in ('RGBA', 'LA', 'P'):
                # Create white background for transparency
                background = Image.new('RGB', img.size, (255, 255, 255))
                if img.mode == 'P':
                    img = img.convert('RGBA')
                background.paste(img, mask=img.split()[-1] if img.mode == 'RGBA' else None)
                img = background
            
            # Get original dimensions
            original_width, original_height = img.size
            print(f"📐 Original dimensions: {original_width}x{original_height}")
            
            # YouTube thumbnail recommendations: 1280x720 (16:9 ratio)
            target_width = 1280
            target_height = 720
            
            # Calculate scaling to fit within target while maintaining aspect ratio
            width_ratio = target_width / original_width
            height_ratio = target_height / original_height
            scale_ratio = min(width_ratio, height_ratio, 1.0)  # Don't upscale
            
            new_width = int(original_width * scale_ratio)
            new_height = int(original_height * scale_ratio)
            
            # Resize if needed
            if scale_ratio < 1.0:
                img = img.resize((new_width, new_height), Image.Resampling.LANCZOS)
                print(f"📐 Resized to: {new_width}x{new_height}")
            
            # Try different quality levels to get under size limit
            quality_levels = [95, 90, 85, 80, 75, 70, 65, 60, 55, 50]
            
            for quality in quality_levels:
                # Save with current quality
                img.save(output_path, 'JPEG', quality=quality, optimize=True)
                
                # Check file size
                compressed_size = os.path.getsize(output_path) / (1024 * 1024)
                
                if compressed_size <= max_size_mb:
                    print(f"✅ Compression successful!")
                    print(f"📊 Final size: {compressed_size:.2f} MB")
                    print(f"📊 Quality used: {quality}%")
                    print(f"📊 Size reduction: {((original_size - compressed_size) / original_size * 100):.1f}%")
                    print(f"💾 Saved as: {output_path}")
                    return True
            
            # If we get here, even 50% quality didn't work - try more aggressive resizing
            print(f"⚠️  Standard compression not sufficient, trying smaller dimensions...")
            
            # Try smaller sizes
            for scale in [0.8, 0.6, 0.5, 0.4]:
                smaller_width = int(new_width * scale)
                smaller_height = int(new_height * scale)
                
                resized_img = img.resize((smaller_width, smaller_height), Image.Resampling.LANCZOS)
                resized_img.save(output_path, 'JPEG', quality=70, optimize=True)
                
                compressed_size = os.path.getsize(output_path) / (1024 * 1024)
                
                if compressed_size <= max_size_mb:
                    print(f"✅ Compression successful with smaller size!")
                    print(f"📐 Final dimensions: {smaller_width}x{smaller_height}")
                    print(f"📊 Final size: {compressed_size:.2f} MB")
                    print(f"📊 Size reduction: {((original_size - compressed_size) / original_size * 100):.1f}%")
                    print(f"💾 Saved as: {output_path}")
                    return True
            
            print(f"❌ Could not compress image below {max_size_mb}MB limit")
            return False
            
    except Exception as e:
        print(f"❌ Error compressing image: {e}")
        return False

def main():
    # Default input path
    input_file = r"C:\Users\<USER>\Downloads\Untitled design (10).png"
    
    # Allow command line argument
    if len(sys.argv) > 1:
        input_file = sys.argv[1]
    
    print("🖼️  YouTube Thumbnail Compressor")
    print("=" * 40)
    
    success = compress_image(input_file)
    
    if success:
        print("\n🎉 Ready for YouTube upload!")
    else:
        print("\n❌ Compression failed")

if __name__ == "__main__":
    main()
