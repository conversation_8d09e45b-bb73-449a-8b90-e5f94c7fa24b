# 🎬 YT Cutter v2.1 - Professional YouTube Video Clipping System

**Transform any YouTube video into high-quality clips with precision timestamps**

A production-ready tool that downloads YouTube videos in optimal quality and creates professional 1080p H.264 clips with universal compatibility. Features intelligent auto-numbering for seamless multi-video organization. Perfect for content creators, social media automation, and video editing workflows.

---

## 🌟 **Key Features**

### 🎯 **Professional Quality Output**
- **True 1080p Resolution** (1920x1080) with 60fps support
- **H.264 Codec** for universal compatibility (plays everywhere)
- **High Bitrate** (~4000k) for crisp, professional quality
- **MP4 Format** optimized for all platforms and devices

### ⚡ **Smart Download Technology**
- **Intelligent Format Selection**: Automatically chooses best quality H.264 source
- **Fallback System**: Gracefully handles quality variations across videos
- **Efficient Processing**: Downloads highest quality, re-encodes for perfect keyframes
- **Smart Source Management**: Interactive choice to keep or delete source files
- **Reuse Existing Downloads**: Skips re-download if source video already exists

### 📁 **Organized Output Structure**
- **Smart Folder Detection**: Automatically detects existing video folders
- **Auto Folder Numbering**: New videos get sequential folder numbers (1, 2, 3...)
- **Sequential Clip Numbering**: Clips numbered 1, 2, 3... within each folder
- **No Duplication**: Never creates multiple folders for the same video
- **Sanitized Filenames**: Cross-platform compatible naming
- **Batch Processing**: Handle multiple clips from single video

---

## 🚀 **Quick Start Guide**

### **1. Configure Your Clips**
Edit `clips_config.json` with your video URL and desired clips:

```json
{
  "url": "https://www.youtube.com/watch?v=rSDI53eMJJI",
  "clips": [
    {
      "name": "Opening Scene",
      "start_time": 15,
      "duration": 30
    },
    {
      "name": "Best Moment",
      "start_time": 120,
      "duration": 45
    },
    {
      "name": "Epic Conclusion",
      "start_time": 300,
      "duration": 25
    }
  ]
}
```

### **2. Run the System**
```bash
python yt_clipper_simple.py
```

### **3. Get Your Clips**
Find your professional-quality clips in automatically numbered folders:
```
Clips/
└── 1. Video Title Name/
    ├── Clip 1 - Opening Scene.mp4
    ├── Clip 2 - Best Moment.mp4
    └── Clip 3 - Epic Conclusion.mp4
```

### **4. Process Multiple Videos**
Each new video gets its own numbered folder automatically:
```
Clips/
├── 1. First Video Title/
│   ├── Clip 1 - Scene A.mp4
│   └── Clip 2 - Scene B.mp4
├── 2. Second Video Title/
│   ├── Clip 1 - Moment A.mp4
│   └── Clip 2 - Moment B.mp4
└── 3. Third Video Title/
    └── Clip 1 - Highlight.mp4
```

### **5. Add More Clips (Same Video)**
Update `clips_config.json` with new clips and run again:
- ✅ **Smart Detection**: Finds existing video folder automatically
- ✅ **Sequential Numbering**: New clips continue from last number (Clip 4, 5, 6...)
- ✅ **No Duplication**: Never creates duplicate folders
- ✅ **Source Reuse**: Skips re-download if you kept the source video

### **6. Interactive Source Management**
After creating clips, you'll be asked:
```
🤔 Do you want to create more clips from this video?
   [y] Yes - Keep source video for more clips
   [n] No - Delete source video to save space
```
- **Choose 'y'**: Keep source for additional clip batches (saves download time)
- **Choose 'n'**: Delete source to free up disk space

---

## 📋 **System Requirements**

### **Included Tools** (No Installation Required)
- ✅ **yt-dlp.exe** - Latest YouTube downloader
- ✅ **ffmpeg.exe** - Professional video processing
- ✅ **Python Script** - Automated workflow management

### **System Compatibility**
- ✅ **Windows** (Primary support)
- ✅ **Cross-platform** file naming
- ✅ **Universal output** (plays on all devices)

---

## 🗂️ **Smart Folder Management**

### **Automatic Folder Numbering**
YT Cutter automatically organizes your clips with intelligent folder numbering:

```
Clips/
├── 1. N3on & Shoreline Mafia Make A BANGER Song/
│   ├── Clip 1 - Opening Bars.mp4
│   ├── Clip 2 - Hook Drop.mp4
│   └── Clip 3 - Final Mix.mp4
├── 2. N3on & Sketch Take a Karate Class/
│   ├── Clip 1 - Dad Thinks He Does Cocaine.mp4
│   ├── Clip 2 - White Belt Disappointment.mp4
│   └── Clip 3 - Wrestling Match.mp4
└── 3. Next Video Title/
    └── Clip 1 - First Moment.mp4
```

### **How It Works**
- **New Videos**: Automatically get the next sequential folder number
- **Same Video**: Detected by title match, clips continue numbering in existing folder
- **No Duplicates**: Never creates multiple folders for the same video
- **Clean Organization**: Perfect for managing multiple video projects

---

## 🎛️ **Configuration Reference**

### **clips_config.json Structure**
```json
{
  "url": "YouTube video URL",
  "clips": [
    {
      "name": "Descriptive clip name",
      "start_time": 60,    // Start time in seconds
      "duration": 30       // Clip length in seconds
    }
  ]
}
```

### **Timing Guidelines**
- **start_time**: Exact second to begin clip (supports decimals: 15.5)
- **duration**: Length of clip in seconds (supports decimals: 30.25)
- **Precision**: Frame-accurate cutting with ffmpeg
- **Validation**: System checks for valid timestamps

---

## 🔧 **Technical Specifications**

### **Download Quality Optimization**
```
Format Selection: "bv*[height<=1080][vcodec^=avc1][ext=mp4]+ba[ext=m4a]/bv*[height<=1080][ext=mp4]+ba[ext=m4a]/b[height<=1080]/b"
```

**Priority Chain:**
1. **1080p H.264 MP4** + **AAC Audio** (Preferred)
2. **1080p Any Codec MP4** + **AAC Audio** (Fallback)
3. **Best Available ≤1080p** (Final fallback)

### **Clipping Process**
- **Method**: High-quality re-encoding with keyframe optimization
- **Quality**: CRF 18 encoding for near-lossless quality
- **Speed**: Fast preset for efficient processing
- **Accuracy**: Frame-perfect timestamp cutting with instant playback

### **Output Specifications**
- **Video Codec**: H.264 (AVC1) - Universal compatibility
- **Audio Codec**: AAC - High quality, small size
- **Container**: MP4 - Maximum platform support
- **Resolution**: Up to 1920x1080 (1080p)
- **Frame Rate**: Preserves source (typically 30fps or 60fps)
- **Bitrate**: ~4000k video, ~128k audio

---

## 📁 **Project Structure**

```
YTcutter/
├── 📄 yt_clipper_simple.py     # Main processing script
├── ⚙️ clips_config.json        # Configuration file
├── 🛠️ ffmpeg.exe              # Video processing tool
├── 📥 yt-dlp.exe               # YouTube downloader
├── 📖 README.md                # This documentation
└── 📁 Clips/                   # Output directory
    ├── 📁 1. [First Video]/    # Auto-numbered folders
    │   ├── 🎬 Clip 1 - [Name].mp4
    │   └── 🎬 Clip 2 - [Name].mp4
    ├── 📁 2. [Second Video]/   # Sequential numbering
    │   └── 🎬 Clip 1 - [Name].mp4
    └── 📁 3. [Third Video]/    # Continues automatically
        └── 🎬 Clip 1 - [Name].mp4
```

---

## 💡 **Pro Tips & Best Practices**

### **Quality Optimization**
- ✅ **Always downloads highest available quality** (up to 1080p)
- ✅ **Forces H.264 codec** for maximum compatibility
- ✅ **CRF 18 encoding** for near-lossless quality
- ✅ **Perfect keyframe alignment** for instant playback

### **Workflow Efficiency**
- 🎯 **Preview timestamps** in YouTube before configuring
- 🎯 **Use descriptive names** for easy clip identification
- 🎯 **Batch multiple clips** from same video efficiently
- 🎯 **Check video duration** to ensure valid timestamps

### **File Management**
- 🗂️ **Auto-organized** by video title with sequential folder numbering
- 🗂️ **Smart folder detection** prevents duplicates for same video
- 🗂️ **Sequential clip numbering** within each folder for logical ordering
- 🗂️ **Interactive source management** - choose to keep or delete source files
- 🗂️ **Efficient workflow** for creating multiple clip batches from same video
- 🗂️ **Cross-platform naming** ensures compatibility

---

## 🎬 **Use Cases**

### **Content Creation**
- **Social Media Clips**: Extract viral moments for TikTok, Instagram, Twitter
- **Highlight Reels**: Create best-of compilations from long-form content
- **Tutorial Segments**: Break down educational content into digestible clips

### **Business Applications**
- **Marketing Content**: Extract key moments for promotional materials
- **Training Materials**: Create focused learning segments
- **Content Repurposing**: Transform long videos into multiple short-form pieces

### **Automation Integration**
- **Batch Processing**: Handle multiple videos with different configurations
- **Workflow Integration**: Embed in larger content creation pipelines
- **Quality Assurance**: Consistent output for professional applications

---

## 🛡️ **Quality Guarantees**

### **Universal Compatibility**
- ✅ **All Video Players**: VLC, Windows Media Player, QuickTime
- ✅ **All Browsers**: Chrome, Firefox, Safari, Edge
- ✅ **All Devices**: Desktop, mobile, tablets
- ✅ **All Platforms**: YouTube, TikTok, Instagram, Twitter

### **Professional Standards**
- ✅ **Broadcast Quality**: Suitable for professional use
- ✅ **High Bitrate**: Crisp, clear visuals
- ✅ **Smooth Playback**: Optimized encoding settings
- ✅ **No Artifacts**: Clean, professional output

---

## 📝 **Legal & Usage Notes**

- **Copyright Compliance**: Always respect copyright and obtain proper permissions
- **Fair Use**: Ensure your usage complies with fair use guidelines
- **Platform Terms**: Respect YouTube's Terms of Service
- **Content Rights**: Only process content you have rights to use

---

## 🔄 **Version History**

**Current Version**: Production-Ready Smart Clipping System v2.1
- ✅ **1080p H.264 Output**: Universal compatibility achieved
- ✅ **Auto Folder Numbering**: Sequential folder numbering (1, 2, 3...) for multiple videos
- ✅ **Smart Folder Detection**: Prevents duplicate folders, continues clip numbering
- ✅ **Interactive Source Management**: Choose to keep or delete source files
- ✅ **Efficient Multi-Batch Workflow**: Create multiple clip sets without re-downloading
- ✅ **Keyframe Optimization**: Instant playback, no frozen frames
- ✅ **CRF 18 Encoding**: Near-lossless quality with perfect compatibility
- ✅ **Production Ready**: Stable, reliable operation

---

*Built for content creators who demand professional quality and universal compatibility. Transform any YouTube video into premium clips with precision and efficiency.*
